import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/visa/visa_models.dart';
import 'package:culture_connect/utils/app_theme.dart';

/// Widget for tracking visa application status with timeline
class VisaStatusTracker extends ConsumerWidget {
  final VisaApplication application;
  final bool showDetails;
  final bool compact;

  const VisaStatusTracker({
    super.key,
    required this.application,
    this.showDetails = true,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final statusSteps = _getStatusSteps();

    return Card(
      margin: EdgeInsets.symmetric(
        horizontal: compact ? 8.0 : 16.0,
        vertical: compact ? 4.0 : 8.0,
      ),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        padding: EdgeInsets.all(compact ? 12.0 : 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                // Status icon
                Container(
                  padding: const EdgeInsets.all(8.0),
                  decoration: BoxDecoration(
                    color: _getStatusColor(theme).withAlpha(26),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getStatusIcon(),
                    size: 20,
                    color: _getStatusColor(theme),
                  ),
                ),
                const SizedBox(width: 12),
                
                // Application info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Visa Application',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${application.visaType} - ${application.countryName}',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Current status badge
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8.0,
                    vertical: 4.0,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(theme).withAlpha(26),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    _getStatusDisplayName(),
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: _getStatusColor(theme),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            
            if (!compact) ...[
              const SizedBox(height: 20),
              
              // Progress timeline
              Column(
                children: statusSteps.asMap().entries.map((entry) {
                  final index = entry.key;
                  final step = entry.value;
                  final isLast = index == statusSteps.length - 1;
                  
                  return _buildTimelineStep(
                    theme,
                    step,
                    isLast,
                    index,
                  );
                }).toList(),
              ),
              
              if (showDetails) ...[
                const SizedBox(height: 16),
                
                // Application details
                Container(
                  padding: const EdgeInsets.all(12.0),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceContainerHighest.withAlpha(77),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Application Details',
                        style: theme.textTheme.labelMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 8),
                      
                      _buildDetailRow(
                        theme,
                        'Application ID',
                        application.id.substring(0, 8).toUpperCase(),
                      ),
                      _buildDetailRow(
                        theme,
                        'Submitted',
                        _formatDate(application.submittedAt),
                      ),
                      if (application.expectedProcessingTime != null)
                        _buildDetailRow(
                          theme,
                          'Expected Processing',
                          '${application.expectedProcessingTime} days',
                        ),
                      if (application.trackingNumber != null)
                        _buildDetailRow(
                          theme,
                          'Tracking Number',
                          application.trackingNumber!,
                        ),
                    ],
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTimelineStep(
    ThemeData theme,
    StatusStep step,
    bool isLast,
    int index,
  ) {
    final isCompleted = step.isCompleted;
    final isCurrent = step.isCurrent;
    final stepColor = isCompleted
        ? Colors.green
        : isCurrent
            ? theme.colorScheme.primary
            : theme.colorScheme.onSurfaceVariant.withAlpha(128);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline indicator
        Column(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: isCompleted || isCurrent
                    ? stepColor
                    : Colors.transparent,
                border: Border.all(
                  color: stepColor,
                  width: 2,
                ),
                shape: BoxShape.circle,
              ),
              child: isCompleted
                  ? const Icon(
                      Icons.check,
                      size: 14,
                      color: Colors.white,
                    )
                  : isCurrent
                      ? Container(
                          margin: const EdgeInsets.all(6),
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                        )
                      : null,
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 32,
                color: stepColor.withAlpha(77),
              ),
          ],
        ),
        
        const SizedBox(width: 12),
        
        // Step content
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  step.title,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isCurrent
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface,
                  ),
                ),
                if (step.description.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    step.description,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
                if (step.timestamp != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    _formatDateTime(step.timestamp!),
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(ThemeData theme, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<StatusStep> _getStatusSteps() {
    final steps = <StatusStep>[];
    final currentStatus = application.status;

    // Define all possible steps
    final allSteps = [
      StatusStep(
        title: 'Application Submitted',
        description: 'Your visa application has been submitted successfully',
        isCompleted: true,
        timestamp: application.submittedAt,
      ),
      StatusStep(
        title: 'Document Review',
        description: 'Embassy is reviewing your submitted documents',
        isCompleted: currentStatus.index >= VisaApplicationStatus.documentsReview.index,
        isCurrent: currentStatus == VisaApplicationStatus.documentsReview,
      ),
      StatusStep(
        title: 'Processing',
        description: 'Your application is being processed',
        isCompleted: currentStatus.index >= VisaApplicationStatus.processing.index,
        isCurrent: currentStatus == VisaApplicationStatus.processing,
      ),
      StatusStep(
        title: 'Decision Made',
        description: currentStatus == VisaApplicationStatus.approved
            ? 'Your visa application has been approved'
            : currentStatus == VisaApplicationStatus.rejected
                ? 'Your visa application has been rejected'
                : 'Decision pending',
        isCompleted: currentStatus == VisaApplicationStatus.approved ||
            currentStatus == VisaApplicationStatus.rejected,
        isCurrent: currentStatus == VisaApplicationStatus.approved ||
            currentStatus == VisaApplicationStatus.rejected,
      ),
    ];

    return allSteps;
  }

  IconData _getStatusIcon() {
    switch (application.status) {
      case VisaApplicationStatus.draft:
        return Icons.edit;
      case VisaApplicationStatus.submitted:
        return Icons.send;
      case VisaApplicationStatus.documentsReview:
        return Icons.description;
      case VisaApplicationStatus.processing:
        return Icons.hourglass_empty;
      case VisaApplicationStatus.approved:
        return Icons.check_circle;
      case VisaApplicationStatus.rejected:
        return Icons.cancel;
      case VisaApplicationStatus.cancelled:
        return Icons.block;
    }
  }

  Color _getStatusColor(ThemeData theme) {
    switch (application.status) {
      case VisaApplicationStatus.draft:
        return theme.colorScheme.onSurfaceVariant;
      case VisaApplicationStatus.submitted:
        return Colors.blue;
      case VisaApplicationStatus.documentsReview:
        return Colors.orange;
      case VisaApplicationStatus.processing:
        return Colors.amber;
      case VisaApplicationStatus.approved:
        return Colors.green;
      case VisaApplicationStatus.rejected:
        return theme.colorScheme.error;
      case VisaApplicationStatus.cancelled:
        return Colors.grey;
    }
  }

  String _getStatusDisplayName() {
    switch (application.status) {
      case VisaApplicationStatus.draft:
        return 'Draft';
      case VisaApplicationStatus.submitted:
        return 'Submitted';
      case VisaApplicationStatus.documentsReview:
        return 'Under Review';
      case VisaApplicationStatus.processing:
        return 'Processing';
      case VisaApplicationStatus.approved:
        return 'Approved';
      case VisaApplicationStatus.rejected:
        return 'Rejected';
      case VisaApplicationStatus.cancelled:
        return 'Cancelled';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${_formatDate(dateTime)} at ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

/// Model for status step in timeline
class StatusStep {
  final String title;
  final String description;
  final bool isCompleted;
  final bool isCurrent;
  final DateTime? timestamp;

  const StatusStep({
    required this.title,
    required this.description,
    this.isCompleted = false,
    this.isCurrent = false,
    this.timestamp,
  });
}
