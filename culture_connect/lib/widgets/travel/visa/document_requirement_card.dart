import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/visa/visa_models.dart';
import 'package:culture_connect/utils/app_theme.dart';

/// Widget for displaying document requirement information
class DocumentRequirementCard extends ConsumerWidget {
  final DocumentRequirement requirement;
  final DocumentStatus? status;
  final VoidCallback? onUpload;
  final VoidCallback? onView;
  final VoidCallback? onEdit;
  final bool showActions;
  final bool compact;

  const DocumentRequirementCard({
    super.key,
    required this.requirement,
    this.status,
    this.onUpload,
    this.onView,
    this.onEdit,
    this.showActions = true,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Card(
      margin: EdgeInsets.symmetric(
        horizontal: compact ? 8.0 : 16.0,
        vertical: compact ? 4.0 : 8.0,
      ),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        padding: EdgeInsets.all(compact ? 12.0 : 16.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: _getStatusColor(theme).withAlpha(77),
            width: 1.5,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with status and type
            Row(
              children: [
                // Document type icon
                Container(
                  padding: const EdgeInsets.all(8.0),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer.withAlpha(128),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getDocumentIcon(requirement.type),
                    size: 20,
                    color: theme.colorScheme.onPrimaryContainer,
                  ),
                ),
                const SizedBox(width: 12),
                
                // Document name and status
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        requirement.name,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          // Status indicator
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8.0,
                              vertical: 4.0,
                            ),
                            decoration: BoxDecoration(
                              color: _getStatusColor(theme).withAlpha(26),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  _getStatusIcon(),
                                  size: 12,
                                  color: _getStatusColor(theme),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  _getStatusText(),
                                  style: theme.textTheme.labelSmall?.copyWith(
                                    color: _getStatusColor(theme),
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          
                          // Required indicator
                          if (requirement.isRequired) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6.0,
                                vertical: 2.0,
                              ),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.error.withAlpha(26),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                'Required',
                                style: theme.textTheme.labelSmall?.copyWith(
                                  color: theme.colorScheme.error,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                
                // Actions menu
                if (showActions && !compact)
                  PopupMenuButton<String>(
                    icon: Icon(
                      Icons.more_vert,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    onSelected: _handleAction,
                    itemBuilder: (context) => [
                      if (status?.documentUrl == null)
                        const PopupMenuItem(
                          value: 'upload',
                          child: Row(
                            children: [
                              Icon(Icons.upload_file),
                              SizedBox(width: 8),
                              Text('Upload Document'),
                            ],
                          ),
                        ),
                      if (status?.documentUrl != null) ...[
                        const PopupMenuItem(
                          value: 'view',
                          child: Row(
                            children: [
                              Icon(Icons.visibility),
                              SizedBox(width: 8),
                              Text('View Document'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit),
                              SizedBox(width: 8),
                              Text('Replace Document'),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
              ],
            ),
            
            if (!compact) ...[
              const SizedBox(height: 12),
              
              // Description
              if (requirement.description.isNotEmpty) ...[
                Text(
                  requirement.description,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12),
              ],
              
              // Requirements and specifications
              if (requirement.specifications.isNotEmpty) ...[
                Container(
                  padding: const EdgeInsets.all(12.0),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceContainerHighest.withAlpha(77),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.checklist,
                            size: 16,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Requirements',
                            style: theme.textTheme.labelMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      ...requirement.specifications.map((spec) => Padding(
                        padding: const EdgeInsets.only(bottom: 4.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(
                              Icons.fiber_manual_record,
                              size: 6,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                spec,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
              ],
              
              // Action buttons
              if (showActions) ...[
                Row(
                  children: [
                    if (status?.documentUrl == null) ...[
                      Expanded(
                        child: FilledButton.icon(
                          onPressed: onUpload,
                          icon: const Icon(Icons.upload_file),
                          label: const Text('Upload Document'),
                        ),
                      ),
                    ] else ...[
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: onView,
                          icon: const Icon(Icons.visibility),
                          label: const Text('View Document'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: FilledButton.icon(
                          onPressed: onEdit,
                          icon: const Icon(Icons.edit),
                          label: const Text('Replace'),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  IconData _getDocumentIcon(String type) {
    switch (type.toLowerCase()) {
      case 'passport':
        return Icons.book;
      case 'visa':
        return Icons.description;
      case 'photo':
      case 'photograph':
        return Icons.photo_camera;
      case 'bank_statement':
      case 'financial':
        return Icons.account_balance;
      case 'invitation':
      case 'letter':
        return Icons.mail;
      case 'insurance':
        return Icons.security;
      case 'ticket':
      case 'flight':
        return Icons.flight;
      case 'hotel':
      case 'accommodation':
        return Icons.hotel;
      case 'employment':
      case 'work':
        return Icons.work;
      case 'education':
      case 'academic':
        return Icons.school;
      default:
        return Icons.description;
    }
  }

  IconData _getStatusIcon() {
    if (status == null) return Icons.pending;
    
    switch (status!.status) {
      case VerificationStatus.pending:
        return Icons.pending;
      case VerificationStatus.verified:
        return Icons.check_circle;
      case VerificationStatus.rejected:
        return Icons.cancel;
      case VerificationStatus.expired:
        return Icons.schedule;
    }
  }

  Color _getStatusColor(ThemeData theme) {
    if (status == null) return theme.colorScheme.onSurfaceVariant;
    
    switch (status!.status) {
      case VerificationStatus.pending:
        return Colors.orange;
      case VerificationStatus.verified:
        return Colors.green;
      case VerificationStatus.rejected:
        return theme.colorScheme.error;
      case VerificationStatus.expired:
        return Colors.grey;
    }
  }

  String _getStatusText() {
    if (status == null) return 'Not Uploaded';
    
    switch (status!.status) {
      case VerificationStatus.pending:
        return 'Under Review';
      case VerificationStatus.verified:
        return 'Verified';
      case VerificationStatus.rejected:
        return 'Rejected';
      case VerificationStatus.expired:
        return 'Expired';
    }
  }

  void _handleAction(String action) {
    switch (action) {
      case 'upload':
        onUpload?.call();
        break;
      case 'view':
        onView?.call();
        break;
      case 'edit':
        onEdit?.call();
        break;
    }
  }
}
