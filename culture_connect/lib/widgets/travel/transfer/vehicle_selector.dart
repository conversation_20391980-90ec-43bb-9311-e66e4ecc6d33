import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/transfer/transfer_vehicle.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Enhanced vehicle selector widget with AirBnB-inspired design
class EnhancedVehicleSelector extends StatefulWidget {
  /// The initially selected vehicle type
  final TransferVehicleType? initialVehicleType;

  /// Callback when a vehicle type is selected
  final Function(TransferVehicleType vehicleType) onVehicleSelected;

  /// Whether to show detailed information
  final bool showDetails;

  /// Whether to show pricing information
  final bool showPricing;

  /// Creates a new enhanced vehicle selector
  const EnhancedVehicleSelector({
    super.key,
    this.initialVehicleType,
    required this.onVehicleSelected,
    this.showDetails = true,
    this.showPricing = false,
  });

  @override
  State<EnhancedVehicleSelector> createState() =>
      _EnhancedVehicleSelectorState();
}

class _EnhancedVehicleSelectorState extends State<EnhancedVehicleSelector>
    with TickerProviderStateMixin {
  TransferVehicleType? _selectedVehicleType;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _selectedVehicleType = widget.initialVehicleType;

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Choose Your Vehicle',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Select the perfect vehicle for your journey',
          style: TextStyle(
            fontSize: 14,
            color: AppTheme.textSecondaryColor,
          ),
        ),
        const SizedBox(height: 20),
        SizedBox(
          height: widget.showDetails ? 180 : 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 4),
            itemCount: TransferVehicleType.values.length,
            itemBuilder: (context, index) {
              final type = TransferVehicleType.values[index];
              return _buildEnhancedVehicleCard(type);
            },
          ),
        ),
      ],
    );
  }

  /// Build an enhanced vehicle type card with AirBnB-inspired design
  Widget _buildEnhancedVehicleCard(TransferVehicleType type) {
    final isSelected = _selectedVehicleType == type;

    return GestureDetector(
      onTapDown: (_) => _animationController.forward(),
      onTapUp: (_) => _animationController.reverse(),
      onTapCancel: () => _animationController.reverse(),
      onTap: () {
        setState(() {
          _selectedVehicleType = type;
        });
        widget.onVehicleSelected(type);
      },
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: isSelected ? 1.0 : _scaleAnimation.value,
            child: Container(
              width: 140,
              margin: const EdgeInsets.only(right: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: isSelected
                      ? AppTheme.primaryColor
                      : Colors.grey.withAlpha(51),
                  width: isSelected ? 2 : 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: isSelected
                        ? AppTheme.primaryColor.withAlpha(26)
                        : Colors.black.withAlpha(13),
                    blurRadius: isSelected ? 12 : 8,
                    offset: const Offset(0, 4),
                  ),
                  if (isSelected)
                    BoxShadow(
                      color: AppTheme.primaryColor.withAlpha(8),
                      blurRadius: 24,
                      offset: const Offset(0, 8),
                    ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Vehicle Icon
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: isSelected
                            ? AppTheme.primaryColor.withAlpha(26)
                            : AppTheme.surfaceColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        type.icon,
                        size: 24,
                        color: isSelected
                            ? AppTheme.primaryColor
                            : AppTheme.textSecondaryColor,
                      ),
                    ),
                    const SizedBox(height: 12),

                    // Vehicle Type Name
                    Text(
                      type.displayName,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.w500,
                        color: isSelected
                            ? AppTheme.primaryColor
                            : AppTheme.textPrimaryColor,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    if (widget.showDetails) ...[
                      const SizedBox(height: 8),
                      // Passenger Capacity
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.person,
                            size: 12,
                            color: AppTheme.textSecondaryColor,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${type.typicalPassengerCapacity}',
                            style: const TextStyle(
                              fontSize: 12,
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                          const SizedBox(width: 8),
                          const Icon(
                            Icons.luggage,
                            size: 12,
                            color: AppTheme.textSecondaryColor,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${type.typicalLuggageCapacity}',
                            style: const TextStyle(
                              fontSize: 12,
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ],

                    if (widget.showPricing) ...[
                      const SizedBox(height: 8),
                      Text(
                        _getEstimatedPrice(type),
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// Get estimated price for vehicle type
  String _getEstimatedPrice(TransferVehicleType type) {
    // Mock pricing - in real app, this would come from API
    final basePrices = {
      TransferVehicleType.sedan: 25,
      TransferVehicleType.luxurySedan: 45,
      TransferVehicleType.suv: 35,
      TransferVehicleType.luxurySuv: 65,
      TransferVehicleType.minivan: 40,
      TransferVehicleType.van: 50,
      TransferVehicleType.shuttleBus: 15,
      TransferVehicleType.limousine: 120,
      TransferVehicleType.electric: 30,
    };

    final price = basePrices[type] ?? 25;
    return 'From \$$price';
  }
}

/// Typedef for backward compatibility - use EnhancedVehicleSelector
typedef VehicleSelector = EnhancedVehicleSelector;
