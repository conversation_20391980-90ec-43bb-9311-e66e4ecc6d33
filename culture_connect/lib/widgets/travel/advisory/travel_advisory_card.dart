import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/advisory/travel_advisory_models.dart';
import 'package:culture_connect/utils/app_theme.dart';

/// Widget for displaying a travel advisory card
class TravelAdvisoryCard extends ConsumerWidget {
  final TravelAdvisory advisory;
  final VoidCallback? onTap;
  final bool showCountry;
  final bool compact;

  const TravelAdvisoryCard({
    super.key,
    required this.advisory,
    this.onTap,
    this.showCountry = true,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Card(
      margin: EdgeInsets.symmetric(
        horizontal: compact ? 8.0 : 16.0,
        vertical: compact ? 4.0 : 8.0,
      ),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: EdgeInsets.all(compact ? 12.0 : 16.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Color(advisory.severity.colorValue).withAlpha(77),
              width: 1.5,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with severity and type
              Row(
                children: [
                  // Severity indicator
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8.0,
                      vertical: 4.0,
                    ),
                    decoration: BoxDecoration(
                      color: Color(advisory.severity.colorValue).withAlpha(26),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _getSeverityIcon(advisory.severity),
                          size: 14,
                          color: Color(advisory.severity.colorValue),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          advisory.severity.displayName,
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: Color(advisory.severity.colorValue),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Advisory type
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8.0,
                      vertical: 4.0,
                    ),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceContainerHighest.withAlpha(128),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _getTypeIcon(advisory.type),
                          size: 14,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          advisory.type.displayName,
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  // Time indicator
                  Text(
                    _formatTimeAgo(advisory.issuedAt),
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
              
              if (!compact) ...[
                const SizedBox(height: 12),
                
                // Country name (if showing)
                if (showCountry) ...[
                  Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 16,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        advisory.countryName,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                ],
                
                // Title
                Text(
                  advisory.title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                
                const SizedBox(height: 8),
                
                // Description
                Text(
                  advisory.description,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
                
                // Affected regions (if any)
                if (advisory.affectedRegions.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 4,
                    runSpacing: 4,
                    children: advisory.affectedRegions.take(3).map((region) {
                      return Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6.0,
                          vertical: 2.0,
                        ),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surfaceContainerHighest.withAlpha(77),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(
                          region,
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ],
                
                // Footer with source and expiry
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(
                      Icons.source,
                      size: 14,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      advisory.source,
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const Spacer(),
                    if (advisory.expiresAt != null) ...[
                      Icon(
                        Icons.schedule,
                        size: 14,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Expires ${_formatDate(advisory.expiresAt!)}',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ],
                ),
              ] else ...[
                // Compact view
                const SizedBox(height: 8),
                if (showCountry) ...[
                  Text(
                    advisory.countryName,
                    style: theme.textTheme.labelMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 4),
                ],
                Text(
                  advisory.title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  IconData _getSeverityIcon(AdvisorySeverity severity) {
    switch (severity) {
      case AdvisorySeverity.low:
        return Icons.info_outline;
      case AdvisorySeverity.moderate:
        return Icons.warning_amber;
      case AdvisorySeverity.high:
        return Icons.error_outline;
      case AdvisorySeverity.critical:
        return Icons.dangerous;
    }
  }

  IconData _getTypeIcon(AdvisoryType type) {
    switch (type) {
      case AdvisoryType.safety:
        return Icons.security;
      case AdvisoryType.health:
        return Icons.local_hospital;
      case AdvisoryType.weather:
        return Icons.wb_sunny;
      case AdvisoryType.political:
        return Icons.account_balance;
      case AdvisoryType.natural:
        return Icons.warning;
      case AdvisoryType.transportation:
        return Icons.directions_bus;
      case AdvisoryType.visa:
        return Icons.description;
      case AdvisoryType.currency:
        return Icons.attach_money;
      case AdvisoryType.cultural:
        return Icons.language;
      case AdvisoryType.general:
        return Icons.info;
    }
  }

  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  String _formatDate(DateTime dateTime) {
    final now = DateTime.now();
    final difference = dateTime.difference(now);

    if (difference.inDays > 0) {
      return '${difference.inDays} days';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours';
    } else {
      return 'Soon';
    }
  }
}
