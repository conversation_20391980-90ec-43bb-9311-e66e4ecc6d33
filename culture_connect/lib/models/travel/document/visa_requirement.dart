import 'package:flutter/material.dart';

// Project imports
import 'package:culture_connect/models/travel/document/visa_assistance_models.dart';

/// Enum representing the different visa requirement types
enum VisaRequirementType {
  /// No visa required
  noVisaRequired,

  /// Visa required
  visaRequired,

  /// Visa on arrival
  visaOnArrival,

  /// Electronic visa (e-Visa)
  eVisa,

  /// Visa exemption
  visaExemption,

  /// Special permit required
  specialPermit,
}

/// Extension for visa requirement types
extension VisaRequirementTypeExtension on VisaRequirementType {
  /// Get the display name for the visa requirement type
  String get displayName {
    switch (this) {
      case VisaRequirementType.noVisaRequired:
        return 'No Visa Required';
      case VisaRequirementType.visaRequired:
        return 'Visa Required';
      case VisaRequirementType.visaOnArrival:
        return 'Visa on Arrival';
      case VisaRequirementType.eVisa:
        return 'Electronic Visa (e-Visa)';
      case VisaRequirementType.visaExemption:
        return 'Visa Exemption';
      case VisaRequirementType.specialPermit:
        return 'Special Permit Required';
    }
  }

  /// Get the icon for the visa requirement type
  IconData get icon {
    switch (this) {
      case VisaRequirementType.noVisaRequired:
        return Icons.check_circle;
      case VisaRequirementType.visaRequired:
        return Icons.assignment;
      case VisaRequirementType.visaOnArrival:
        return Icons.flight_land;
      case VisaRequirementType.eVisa:
        return Icons.computer;
      case VisaRequirementType.visaExemption:
        return Icons.verified_user;
      case VisaRequirementType.specialPermit:
        return Icons.assignment_late;
    }
  }

  /// Get the color for the visa requirement type
  Color get color {
    switch (this) {
      case VisaRequirementType.noVisaRequired:
        return Colors.green;
      case VisaRequirementType.visaRequired:
        return Colors.red;
      case VisaRequirementType.visaOnArrival:
        return Colors.orange;
      case VisaRequirementType.eVisa:
        return Colors.blue;
      case VisaRequirementType.visaExemption:
        return Colors.green;
      case VisaRequirementType.specialPermit:
        return Colors.purple;
    }
  }
}

/// A model representing visa requirements between countries
class VisaRequirement {
  /// Unique identifier for the visa requirement
  final String id;

  /// Country of origin (nationality)
  final String countryFrom;

  /// Country of destination
  final String countryTo;

  /// Type of visa requirement
  final VisaRequirementType requirementType;

  /// Description of the visa requirement
  final String description;

  /// Maximum stay duration in days
  final int? maxStayDuration;

  /// Processing time in days
  final int? processingTime;

  /// Visa fee in USD
  final double? visaFee;

  /// Required documents
  final List<String> requiredDocuments;

  /// Application URL
  final String? applicationUrl;

  /// Additional notes
  final String? notes;

  /// When the information was last updated
  final DateTime lastUpdated;

  // Enhanced properties for visa assistance

  /// Embassy/consulate contact information
  final EmbassyContactInfo? embassyInfo;

  /// Document checklist with detailed requirements
  final List<DocumentRequirement> documentChecklist;

  /// Travel advisory information
  final TravelAdvisoryInfo? travelAdvisory;

  /// Appointment booking requirements
  final AppointmentRequirement? appointmentRequirement;

  /// Processing timeline with milestones
  final ProcessingTimeline? processingTimeline;

  /// Emergency contact information
  final EmergencyContactInfo? emergencyContact;

  /// Digital application availability
  final bool hasDigitalApplication;

  /// Biometric requirements
  final BiometricRequirement? biometricRequirement;

  /// Interview requirements
  final InterviewRequirement? interviewRequirement;

  /// Health requirements (vaccinations, medical certificates)
  final List<HealthRequirement> healthRequirements;

  /// Financial requirements (bank statements, proof of funds)
  final FinancialRequirement? financialRequirement;

  /// Travel insurance requirements
  final InsuranceRequirement? insuranceRequirement;

  /// Creates a new visa requirement
  const VisaRequirement({
    required this.id,
    required this.countryFrom,
    required this.countryTo,
    required this.requirementType,
    required this.description,
    this.maxStayDuration,
    this.processingTime,
    this.visaFee,
    required this.requiredDocuments,
    this.applicationUrl,
    this.notes,
    required this.lastUpdated,
    // Enhanced properties
    this.embassyInfo,
    this.documentChecklist = const [],
    this.travelAdvisory,
    this.appointmentRequirement,
    this.processingTimeline,
    this.emergencyContact,
    this.hasDigitalApplication = false,
    this.biometricRequirement,
    this.interviewRequirement,
    this.healthRequirements = const [],
    this.financialRequirement,
    this.insuranceRequirement,
  });

  /// Create a visa requirement from a JSON map
  factory VisaRequirement.fromJson(Map<String, dynamic> json) {
    return VisaRequirement(
      id: json['id'] as String,
      countryFrom: json['countryFrom'] as String,
      countryTo: json['countryTo'] as String,
      requirementType: VisaRequirementType.values.firstWhere(
        (e) => e.toString() == 'VisaRequirementType.${json['requirementType']}',
        orElse: () => VisaRequirementType.visaRequired,
      ),
      description: json['description'] as String,
      maxStayDuration: json['maxStayDuration'] as int?,
      processingTime: json['processingTime'] as int?,
      visaFee: json['visaFee'] as double?,
      requiredDocuments: (json['requiredDocuments'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      applicationUrl: json['applicationUrl'] as String?,
      notes: json['notes'] as String?,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  /// Create a visa requirement from Travel Buddy API response
  factory VisaRequirement.fromTravelBuddyApi(Map<String, dynamic> json) {
    // Map Travel Buddy API visa requirement types to our enum
    VisaRequirementType mapRequirementType(String? apiType) {
      switch (apiType?.toLowerCase()) {
        case 'no_visa_required':
        case 'visa_free':
          return VisaRequirementType.noVisaRequired;
        case 'visa_required':
        case 'visa_required_in_advance':
          return VisaRequirementType.visaRequired;
        case 'visa_on_arrival':
        case 'voa':
          return VisaRequirementType.visaOnArrival;
        case 'evisa':
        case 'e_visa':
        case 'electronic_visa':
          return VisaRequirementType.eVisa;
        case 'visa_exemption':
        case 'exemption':
          return VisaRequirementType.visaExemption;
        case 'special_permit':
        case 'permit_required':
          return VisaRequirementType.specialPermit;
        default:
          return VisaRequirementType.visaRequired;
      }
    }

    // Generate unique ID from passport and destination countries
    final passportCountry = json['passport_country'] as String? ?? '';
    final destinationCountry = json['destination_country'] as String? ?? '';
    final id = '${passportCountry}_$destinationCountry';

    return VisaRequirement(
      id: id,
      countryFrom: passportCountry,
      countryTo: destinationCountry,
      requirementType: mapRequirementType(json['visa_requirement'] as String?),
      description: json['description'] as String? ??
          json['summary'] as String? ??
          'Visa requirement information',
      maxStayDuration:
          json['max_stay_days'] as int? ?? json['allowed_stay'] as int?,
      processingTime: json['processing_time_days'] as int? ??
          json['processing_time'] as int?,
      visaFee: (json['visa_fee'] as num?)?.toDouble() ??
          (json['fee_usd'] as num?)?.toDouble(),
      requiredDocuments:
          _parseDocumentsList(json['required_documents'] ?? json['documents']),
      applicationUrl:
          json['application_url'] as String? ?? json['embassy_url'] as String?,
      notes: json['notes'] as String? ?? json['additional_info'] as String?,
      lastUpdated:
          _parseLastUpdated(json['last_updated'] ?? json['updated_at']),
      // Enhanced properties with null safety
      embassyInfo: json['embassyInfo'] != null
          ? EmbassyContactInfo.fromJson(
              json['embassyInfo'] as Map<String, dynamic>)
          : null,
      documentChecklist: json['documentChecklist'] != null
          ? (json['documentChecklist'] as List<dynamic>)
              .map((e) =>
                  DocumentRequirement.fromJson(e as Map<String, dynamic>))
              .toList()
          : const [],
      travelAdvisory: json['travelAdvisory'] != null
          ? TravelAdvisoryInfo.fromJson(
              json['travelAdvisory'] as Map<String, dynamic>)
          : null,
      appointmentRequirement: json['appointmentRequirement'] != null
          ? AppointmentRequirement.fromJson(
              json['appointmentRequirement'] as Map<String, dynamic>)
          : null,
      processingTimeline: json['processingTimeline'] != null
          ? ProcessingTimeline.fromJson(
              json['processingTimeline'] as Map<String, dynamic>)
          : null,
      emergencyContact: json['emergencyContact'] != null
          ? EmergencyContactInfo.fromJson(
              json['emergencyContact'] as Map<String, dynamic>)
          : null,
      hasDigitalApplication: json['hasDigitalApplication'] as bool? ?? false,
      biometricRequirement: json['biometricRequirement'] != null
          ? BiometricRequirement.fromJson(
              json['biometricRequirement'] as Map<String, dynamic>)
          : null,
      interviewRequirement: json['interviewRequirement'] != null
          ? InterviewRequirement.fromJson(
              json['interviewRequirement'] as Map<String, dynamic>)
          : null,
      healthRequirements: json['healthRequirements'] != null
          ? (json['healthRequirements'] as List<dynamic>)
              .map((e) => HealthRequirement.fromJson(e as Map<String, dynamic>))
              .toList()
          : const [],
      financialRequirement: json['financialRequirement'] != null
          ? FinancialRequirement.fromJson(
              json['financialRequirement'] as Map<String, dynamic>)
          : null,
      insuranceRequirement: json['insuranceRequirement'] != null
          ? InsuranceRequirement.fromJson(
              json['insuranceRequirement'] as Map<String, dynamic>)
          : null,
    );
  }

  /// Parse documents list from Travel Buddy API
  static List<String> _parseDocumentsList(dynamic documents) {
    if (documents == null) return [];

    if (documents is List) {
      return documents.map((doc) => doc.toString()).toList();
    } else if (documents is String) {
      // If it's a comma-separated string, split it
      return documents.split(',').map((doc) => doc.trim()).toList();
    }

    return [];
  }

  /// Parse last updated date from Travel Buddy API
  static DateTime _parseLastUpdated(dynamic lastUpdated) {
    if (lastUpdated == null) return DateTime.now();

    if (lastUpdated is String) {
      try {
        return DateTime.parse(lastUpdated);
      } catch (e) {
        return DateTime.now();
      }
    }

    return DateTime.now();
  }

  /// Convert the visa requirement to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'countryFrom': countryFrom,
      'countryTo': countryTo,
      'requirementType': requirementType.toString().split('.').last,
      'description': description,
      'maxStayDuration': maxStayDuration,
      'processingTime': processingTime,
      'visaFee': visaFee,
      'requiredDocuments': requiredDocuments,
      'applicationUrl': applicationUrl,
      'notes': notes,
      'lastUpdated': lastUpdated.toIso8601String(),
      // Enhanced properties
      'embassyInfo': embassyInfo?.toJson(),
      'documentChecklist': documentChecklist.map((e) => e.toJson()).toList(),
      'travelAdvisory': travelAdvisory?.toJson(),
      'appointmentRequirement': appointmentRequirement?.toJson(),
      'processingTimeline': processingTimeline?.toJson(),
      'emergencyContact': emergencyContact?.toJson(),
      'hasDigitalApplication': hasDigitalApplication,
      'biometricRequirement': biometricRequirement?.toJson(),
      'interviewRequirement': interviewRequirement?.toJson(),
      'healthRequirements': healthRequirements.map((e) => e.toJson()).toList(),
      'financialRequirement': financialRequirement?.toJson(),
      'insuranceRequirement': insuranceRequirement?.toJson(),
    };
  }

  /// Get the formatted maximum stay duration
  String? get formattedMaxStayDuration {
    if (maxStayDuration == null) return null;

    if (maxStayDuration! >= 365) {
      final years = (maxStayDuration! / 365).floor();
      final days = maxStayDuration! % 365;
      if (days == 0) {
        return '$years ${years == 1 ? 'year' : 'years'}';
      } else {
        return '$years ${years == 1 ? 'year' : 'years'} and $days ${days == 1 ? 'day' : 'days'}';
      }
    } else if (maxStayDuration! >= 30) {
      final months = (maxStayDuration! / 30).floor();
      final days = maxStayDuration! % 30;
      if (days == 0) {
        return '$months ${months == 1 ? 'month' : 'months'}';
      } else {
        return '$months ${months == 1 ? 'month' : 'months'} and $days ${days == 1 ? 'day' : 'days'}';
      }
    } else {
      return '$maxStayDuration ${maxStayDuration == 1 ? 'day' : 'days'}';
    }
  }

  /// Get the formatted processing time
  String? get formattedProcessingTime {
    if (processingTime == null) return null;

    if (processingTime! >= 30) {
      final months = (processingTime! / 30).floor();
      final days = processingTime! % 30;
      if (days == 0) {
        return '$months ${months == 1 ? 'month' : 'months'}';
      } else {
        return '$months ${months == 1 ? 'month' : 'months'} and $days ${days == 1 ? 'day' : 'days'}';
      }
    } else {
      return '$processingTime ${processingTime == 1 ? 'day' : 'days'}';
    }
  }

  /// Get the formatted visa fee
  String? get formattedVisaFee {
    if (visaFee == null) return null;
    return '\$${visaFee!.toStringAsFixed(2)}';
  }

  /// Create a copy of this visa requirement with the given fields replaced with new values
  VisaRequirement copyWith({
    String? id,
    String? countryFrom,
    String? countryTo,
    VisaRequirementType? requirementType,
    String? description,
    int? maxStayDuration,
    int? processingTime,
    double? visaFee,
    List<String>? requiredDocuments,
    String? applicationUrl,
    String? notes,
    DateTime? lastUpdated,
  }) {
    return VisaRequirement(
      id: id ?? this.id,
      countryFrom: countryFrom ?? this.countryFrom,
      countryTo: countryTo ?? this.countryTo,
      requirementType: requirementType ?? this.requirementType,
      description: description ?? this.description,
      maxStayDuration: maxStayDuration ?? this.maxStayDuration,
      processingTime: processingTime ?? this.processingTime,
      visaFee: visaFee ?? this.visaFee,
      requiredDocuments: requiredDocuments ?? this.requiredDocuments,
      applicationUrl: applicationUrl ?? this.applicationUrl,
      notes: notes ?? this.notes,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}
