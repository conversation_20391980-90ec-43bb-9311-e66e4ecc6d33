import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/visa/visa_models.dart';
import 'package:culture_connect/models/travel/document/visa_assistance_models.dart';
import 'package:culture_connect/models/travel/advisory/travel_advisory_models.dart';
import 'package:culture_connect/widgets/travel/visa/document_requirement_card.dart';
import 'package:culture_connect/widgets/travel/visa/visa_status_tracker.dart';
import 'package:culture_connect/widgets/travel/advisory/travel_advisory_list.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_display.dart';

/// Main screen for visa and travel document assistance
class VisaAssistanceScreen extends ConsumerStatefulWidget {
  const VisaAssistanceScreen({super.key});

  @override
  ConsumerState<VisaAssistanceScreen> createState() =>
      _VisaAssistanceScreenState();
}

class _VisaAssistanceScreenState extends ConsumerState<VisaAssistanceScreen> {
  bool _isLoading = true;
  String? _errorMessage;
  List<VisaApplication> _applications = [];
  List<DocumentRequirement> _requirements = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Load mock data for demo purposes
      await Future.delayed(const Duration(milliseconds: 500));

      final applications = _getMockApplications();
      final requirements = _getMockRequirements();

      setState(() {
        _applications = applications;
        _requirements = requirements;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Visa Assistance'),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // Navigate to notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () {
              // Navigate to help
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : _errorMessage != null
              ? Center(
                  child: ErrorDisplay(
                    message: _errorMessage!,
                    showRetry: true,
                    onRetry: _loadData,
                  ),
                )
              : RefreshIndicator(
                  onRefresh: _loadData,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildHeroSection(theme),
                        const SizedBox(height: 24),
                        _buildQuickActions(theme),
                        const SizedBox(height: 24),
                        _buildActiveApplications(theme),
                        const SizedBox(height: 24),
                        _buildDocumentRequirements(theme),
                        const SizedBox(height: 24),
                        _buildTravelAdvisories(theme),
                        const SizedBox(height: 24),
                        _buildVisaServices(theme),
                      ],
                    ),
                  ),
                ),
    );
  }

  Widget _buildHeroSection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primary,
            theme.colorScheme.primary.withAlpha(204),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.primary.withAlpha(51),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.description,
                size: 32,
                color: theme.colorScheme.onPrimary,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Visa & Document Assistance',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        color: theme.colorScheme.onPrimary,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Get help with visa applications, document verification, and travel requirements',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onPrimary.withAlpha(230),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildStatCard(
                theme,
                'Active Applications',
                _applications
                    .where((app) =>
                        app.status != VisaApplicationStatus.approved &&
                        app.status != VisaApplicationStatus.rejected)
                    .length
                    .toString(),
                Icons.pending_actions,
              ),
              const SizedBox(width: 12),
              _buildStatCard(
                theme,
                'Documents Pending',
                _requirements.where((req) => req.isMandatory).length.toString(),
                Icons.upload_file,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
      ThemeData theme, String label, String value, IconData icon) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: theme.colorScheme.onPrimary.withAlpha(26),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: 20,
              color: theme.colorScheme.onPrimary,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    value,
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.onPrimary,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  Text(
                    label,
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: theme.colorScheme.onPrimary.withAlpha(204),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            _buildActionCard(
              theme,
              'Check Requirements',
              'Verify visa requirements for your destination',
              Icons.checklist,
              () => _navigateToRequirementsChecker(),
            ),
            const SizedBox(width: 12),
            _buildActionCard(
              theme,
              'Upload Documents',
              'Submit required documents for verification',
              Icons.upload_file,
              () => _navigateToDocumentUpload(),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            _buildActionCard(
              theme,
              'Schedule Appointment',
              'Book embassy or consulate appointments',
              Icons.calendar_today,
              () => _navigateToAppointmentScheduler(),
            ),
            const SizedBox(width: 12),
            _buildActionCard(
              theme,
              'Track Application',
              'Monitor your visa application status',
              Icons.track_changes,
              () => _navigateToApplicationTracker(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    ThemeData theme,
    String title,
    String description,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Expanded(
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer.withAlpha(128),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    size: 24,
                    color: theme.colorScheme.onPrimaryContainer,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  title,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActiveApplications(ThemeData theme) {
    final activeApplications = _applications
        .where((app) =>
            app.status != VisaApplicationStatus.approved &&
            app.status != VisaApplicationStatus.rejected)
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Active Applications',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            if (activeApplications.isNotEmpty)
              TextButton(
                onPressed: () => _navigateToAllApplications(),
                child: const Text('View All'),
              ),
          ],
        ),
        const SizedBox(height: 12),
        if (activeApplications.isEmpty) ...[
          _buildEmptyState(
            theme,
            'No Active Applications',
            'Start a new visa application to track your progress',
            Icons.description,
            () => _navigateToNewApplication(),
            'Start Application',
          ),
        ] else ...[
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount:
                activeApplications.length > 2 ? 2 : activeApplications.length,
            itemBuilder: (context, index) {
              final application = activeApplications[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: VisaStatusTracker(
                  application: application,
                  compact: true,
                  showDetails: false,
                ),
              );
            },
          ),
        ],
      ],
    );
  }

  Widget _buildDocumentRequirements(ThemeData theme) {
    final pendingRequirements =
        _requirements.where((req) => req.isMandatory).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Document Requirements',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            if (pendingRequirements.isNotEmpty)
              TextButton(
                onPressed: () => _navigateToAllDocuments(),
                child: const Text('View All'),
              ),
          ],
        ),
        const SizedBox(height: 12),
        if (pendingRequirements.isEmpty) ...[
          _buildEmptyState(
            theme,
            'No Pending Documents',
            'All required documents have been submitted',
            Icons.check_circle,
            null,
            null,
          ),
        ] else ...[
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount:
                pendingRequirements.length > 3 ? 3 : pendingRequirements.length,
            itemBuilder: (context, index) {
              final requirement = pendingRequirements[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: DocumentRequirementCard(
                  requirement: requirement,
                  compact: true,
                  onUpload: () => _navigateToDocumentUpload(),
                ),
              );
            },
          ),
        ],
      ],
    );
  }

  Widget _buildTravelAdvisories(ThemeData theme) {
    return TravelAdvisoryHorizontalList(
      title: 'Travel Advisories',
      severityLevels: const [AdvisorySeverity.high, AdvisorySeverity.critical],
      onAdvisoryTap: (advisory) => _navigateToAdvisoryDetails(advisory),
      onSeeAll: () => _navigateToAllAdvisories(),
    );
  }

  Widget _buildVisaServices(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Visa Services',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            _buildServiceCard(
              theme,
              'Service Providers',
              'Find verified visa service providers',
              Icons.business,
              () => _navigateToServiceProviders(),
            ),
            const SizedBox(width: 12),
            _buildServiceCard(
              theme,
              'Embassy Locator',
              'Find embassies and consulates',
              Icons.location_on,
              () => _navigateToEmbassyLocator(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildServiceCard(
    ThemeData theme,
    String title,
    String description,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Expanded(
      child: Card(
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  icon,
                  size: 32,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(height: 12),
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(
    ThemeData theme,
    String title,
    String message,
    IconData icon,
    VoidCallback? onAction,
    String? actionLabel,
  ) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withAlpha(77),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 48,
            color: theme.colorScheme.onSurfaceVariant.withAlpha(128),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            message,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant.withAlpha(179),
            ),
            textAlign: TextAlign.center,
          ),
          if (onAction != null && actionLabel != null) ...[
            const SizedBox(height: 16),
            FilledButton(
              onPressed: onAction,
              child: Text(actionLabel),
            ),
          ],
        ],
      ),
    );
  }

  // Navigation methods
  void _navigateToRequirementsChecker() {
    // TODO: Navigate to requirements checker
  }

  void _navigateToDocumentUpload() {
    // TODO: Navigate to document upload
  }

  void _navigateToAppointmentScheduler() {
    // TODO: Navigate to appointment scheduler
  }

  void _navigateToApplicationTracker() {
    // TODO: Navigate to application tracker
  }

  void _navigateToAllApplications() {
    // TODO: Navigate to all applications
  }

  void _navigateToNewApplication() {
    // TODO: Navigate to new application
  }

  void _navigateToAllDocuments() {
    // TODO: Navigate to all documents
  }

  void _navigateToAdvisoryDetails(TravelAdvisory advisory) {
    // TODO: Navigate to advisory details
  }

  void _navigateToAllAdvisories() {
    // TODO: Navigate to all advisories
  }

  void _navigateToServiceProviders() {
    // TODO: Navigate to service providers
  }

  void _navigateToEmbassyLocator() {
    // TODO: Navigate to embassy locator
  }

  // Mock data methods
  List<VisaApplication> _getMockApplications() {
    final now = DateTime.now();
    return [
      VisaApplication(
        id: '1',
        userId: 'user1',
        destinationCountry: 'United States',
        visaType: 'Tourist',
        embassy: 'US Embassy',
        baseFee: 160.0,
        serviceFee: 50.0,
        processingFee: 25.0,
        totalFees: 235.0,
        currency: 'USD',
        status: VisaApplicationStatus.underReview,
        paymentStatus: VisaPaymentStatus.paid,
        paymentReference: 'PAY_US2024001',
        paidAt: now.subtract(const Duration(days: 5)),
        createdAt: now.subtract(const Duration(days: 5)),
        updatedAt: now.subtract(const Duration(days: 2)),
      ),
      VisaApplication(
        id: '2',
        userId: 'user1',
        destinationCountry: 'United Kingdom',
        visaType: 'Business',
        embassy: 'UK Consulate',
        baseFee: 95.0,
        serviceFee: 40.0,
        processingFee: 20.0,
        totalFees: 155.0,
        currency: 'GBP',
        status: VisaApplicationStatus.submitted,
        paymentStatus: VisaPaymentStatus.paid,
        paymentReference: 'PAY_UK2024002',
        paidAt: now.subtract(const Duration(days: 12)),
        createdAt: now.subtract(const Duration(days: 12)),
        updatedAt: now.subtract(const Duration(days: 8)),
      ),
    ];
  }

  List<DocumentRequirement> _getMockRequirements() {
    return [
      const DocumentRequirement(
        documentType: 'passport',
        displayName: 'Passport',
        description: 'Valid passport with at least 6 months validity',
        isMandatory: true,
        acceptedFormats: ['jpg', 'png', 'pdf'],
        maxFileSizeMB: 5.0,
        specificRequirements: ['Color copy', 'All pages'],
      ),
      const DocumentRequirement(
        documentType: 'photo',
        displayName: 'Passport Photo',
        description: 'Recent passport-sized photograph',
        isMandatory: true,
        acceptedFormats: ['jpg', 'png'],
        maxFileSizeMB: 2.0,
        specificRequirements: ['White background', '2x2 inches'],
      ),
      const DocumentRequirement(
        documentType: 'bank_statement',
        displayName: 'Bank Statement',
        description: 'Recent bank statement showing financial stability',
        isMandatory: false,
        acceptedFormats: ['pdf'],
        maxFileSizeMB: 10.0,
        specificRequirements: ['Last 3 months', 'Official letterhead'],
      ),
    ];
  }
}
