import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/security_settings.dart';
import 'package:culture_connect/providers/security_settings_provider.dart';
import 'package:culture_connect/services/auth_service.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Security settings screen for configuring auto-lock and authentication preferences
class SecuritySettingsScreen extends ConsumerStatefulWidget {
  const SecuritySettingsScreen({super.key});

  @override
  ConsumerState<SecuritySettingsScreen> createState() =>
      _SecuritySettingsScreenState();
}

class _SecuritySettingsScreenState
    extends ConsumerState<SecuritySettingsScreen> {
  bool _biometricAvailable = false;

  @override
  void initState() {
    super.initState();
    _checkBiometricAvailability();
  }

  /// Check if biometric authentication is available on this device
  Future<void> _checkBiometricAvailability() async {
    try {
      final authService = AuthService();
      final isAvailable = await authService.isBiometricAvailable();
      if (mounted) {
        setState(() {
          _biometricAvailable = isAvailable;
        });
      }
    } catch (e) {
      debugPrint('Error checking biometric availability: $e');
    }
  }

  /// Show timeout selection dialog
  Future<void> _showTimeoutDialog() async {
    final currentSettings = ref.read(securitySettingsProvider);
    final selectedTimeout = await showDialog<int>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Auto-Lock Timeout'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: SecuritySettings.timeoutOptions.map((minutes) {
            final isSelected =
                minutes == currentSettings.autoLockTimeoutMinutes;
            final displayText = SecuritySettings.timeoutDisplayTexts[
                SecuritySettings.timeoutOptions.indexOf(minutes)];

            return RadioListTile<int>(
              title: Text(displayText),
              value: minutes,
              groupValue: currentSettings.autoLockTimeoutMinutes,
              onChanged: (value) => Navigator.of(context).pop(value),
              activeColor: AppTheme.primaryColor,
              selected: isSelected,
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );

    if (selectedTimeout != null) {
      await ref
          .read(securitySettingsProvider.notifier)
          .setAutoLockTimeout(selectedTimeout);
    }
  }

  @override
  Widget build(BuildContext context) {
    final securitySettings = ref.watch(securitySettingsProvider);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text(
          'Security Settings',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        backgroundColor: AppTheme.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios,
              color: AppTheme.textPrimaryColor),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Auto-Lock Section
          _buildSectionHeader('Auto-Lock'),
          const SizedBox(height: 8),

          _buildSettingCard(
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text(
                    'Enable Auto-Lock',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  subtitle: const Text(
                    'Automatically lock the app after inactivity',
                    style: TextStyle(color: AppTheme.textSecondaryColor),
                  ),
                  value: securitySettings.autoLockEnabled,
                  onChanged: (value) {
                    ref
                        .read(securitySettingsProvider.notifier)
                        .setAutoLockEnabled(value);
                  },
                  activeColor: AppTheme.primaryColor,
                ),
                if (securitySettings.autoLockEnabled) ...[
                  const Divider(height: 1),
                  ListTile(
                    title: const Text(
                      'Auto-Lock Timeout',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    subtitle: Text(
                      securitySettings.timeoutDisplayText,
                      style:
                          const TextStyle(color: AppTheme.textSecondaryColor),
                    ),
                    trailing: const Icon(Icons.chevron_right,
                        color: AppTheme.textSecondaryColor),
                    onTap: _showTimeoutDialog,
                  ),
                  const Divider(height: 1),
                  SwitchListTile(
                    title: const Text(
                      'Lock on App Background',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    subtitle: const Text(
                      'Lock when app goes to background',
                      style: TextStyle(color: AppTheme.textSecondaryColor),
                    ),
                    value: securitySettings.lockOnAppBackground,
                    onChanged: (value) {
                      ref
                          .read(securitySettingsProvider.notifier)
                          .setLockOnAppBackground(value);
                    },
                    activeColor: AppTheme.primaryColor,
                  ),
                ],
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Authentication Section
          _buildSectionHeader('Authentication'),
          const SizedBox(height: 8),

          _buildSettingCard(
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text(
                    'Biometric Authentication',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  subtitle: Text(
                    _biometricAvailable
                        ? 'Use fingerprint or face recognition'
                        : 'Not available on this device',
                    style: TextStyle(
                      color: _biometricAvailable
                          ? AppTheme.textSecondaryColor
                          : AppTheme.errorColor,
                    ),
                  ),
                  value:
                      securitySettings.biometricEnabled && _biometricAvailable,
                  onChanged: _biometricAvailable
                      ? (value) {
                          ref
                              .read(securitySettingsProvider.notifier)
                              .setBiometricEnabled(value);
                        }
                      : null,
                  activeColor: AppTheme.primaryColor,
                ),
                const Divider(height: 1),
                SwitchListTile(
                  title: const Text(
                    'Require Password Fallback',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  subtitle: const Text(
                    'Always show password option',
                    style: TextStyle(color: AppTheme.textSecondaryColor),
                  ),
                  value: securitySettings.requirePasswordFallback,
                  onChanged: (value) {
                    ref
                        .read(securitySettingsProvider.notifier)
                        .setRequirePasswordFallback(value);
                  },
                  activeColor: AppTheme.primaryColor,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Privacy Section
          _buildSectionHeader('Privacy'),
          const SizedBox(height: 8),

          _buildSettingCard(
            child: SwitchListTile(
              title: const Text(
                'Show Lock Screen Preview',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              subtitle: const Text(
                'Show app content in recent apps',
                style: TextStyle(color: AppTheme.textSecondaryColor),
              ),
              value: securitySettings.showLockScreenPreview,
              onChanged: (value) {
                ref
                    .read(securitySettingsProvider.notifier)
                    .setShowLockScreenPreview(value);
              },
              activeColor: AppTheme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  /// Build section header
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(left: 4, bottom: 4),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: AppTheme.textPrimaryColor,
        ),
      ),
    );
  }

  /// Build setting card container
  Widget _buildSettingCard({required Widget child}) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 opacity
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: child,
    );
  }
}
